document.addEventListener("DOMContentLoaded", () => {
  const hamburgerMenu = document.querySelector(".hamburger-menu")
  const navLinks = document.querySelector(".nav-links")
  const header = document.querySelector(".header")

  // Hero Section Animation
  initHeroAnimations()

  // Create a mobile navigation overlay
  const navOverlay = document.createElement("div")
  navOverlay.classList.add("nav-overlay")
  navOverlay.innerHTML = `
        <a href="#features">Features</a>
        <a href="#testimonials">Testimonials</a>
        <a href="#pricing">Pricing</a>
        <a href="#contact">Contact</a>
        <button class="btn btn-ghost">Sign In</button>
        <button class="btn btn-primary">Start Free Trial</button>
    `
  document.body.appendChild(navOverlay)

  hamburgerMenu.addEventListener("click", () => {
    hamburgerMenu.classList.toggle("open")
    navOverlay.classList.toggle("open")
    document.body.classList.toggle("no-scroll") // Prevent scrolling when menu is open
  })

  // Close mobile menu when a link is clicked
  navOverlay.querySelectorAll("a").forEach((link) => {
    link.addEventListener("click", () => {
      hamburgerMenu.classList.remove("open")
      navOverlay.classList.remove("open")
      document.body.classList.remove("no-scroll")
    })
  })

  // Close mobile menu when "Sign In" or "Start Free Trial" buttons are clicked in the overlay
  navOverlay.querySelectorAll(".btn").forEach((button) => {
    button.addEventListener("click", () => {
      hamburgerMenu.classList.remove("open")
      navOverlay.classList.remove("open")
      document.body.classList.remove("no-scroll")
    })
  })

  // Add a class to body to prevent scrolling when mobile menu is open
  const style = document.createElement("style")
  style.innerHTML = `
        body.no-scroll {
            overflow: hidden;
        }
    `
  document.head.appendChild(style)
})

// Hero Section Animation Function
function initHeroAnimations() {
  // Initialize the hero slider
  initHeroSlider()

  // Apply initial animations to the first slide
  animateCurrentSlide()
}

// Hero Slider Functionality
function initHeroSlider() {
  const slides = document.querySelectorAll('.hero-slide')
  const navDots = document.querySelectorAll('.nav-dot')
  let currentSlide = 0
  let slideInterval

  // Auto-play functionality
  function startAutoPlay() {
    slideInterval = setInterval(() => {
      nextSlide()
    }, 5000) // 5 seconds
  }

  function stopAutoPlay() {
    clearInterval(slideInterval)
  }

  // Navigate to specific slide
  function goToSlide(slideIndex) {
    // Remove active class from current slide and nav dot
    slides[currentSlide].classList.remove('active')
    navDots[currentSlide].classList.remove('active')

    // Add prev class to current slide for exit animation
    slides[currentSlide].classList.add('prev')

    // Update current slide index
    currentSlide = slideIndex

    // Remove prev class from all slides
    slides.forEach(slide => slide.classList.remove('prev'))

    // Add active class to new slide and nav dot
    slides[currentSlide].classList.add('active')
    navDots[currentSlide].classList.add('active')

    // Animate the new slide content
    setTimeout(() => {
      animateCurrentSlide()
    }, 100)
  }

  // Next slide function
  function nextSlide() {
    const nextIndex = (currentSlide + 1) % slides.length
    goToSlide(nextIndex)
  }

  // Add click event listeners to navigation dots
  navDots.forEach((dot, index) => {
    dot.addEventListener('click', () => {
      stopAutoPlay()
      goToSlide(index)
      startAutoPlay() // Restart auto-play after manual navigation
    })
  })

  // Pause auto-play on hover
  const heroSection = document.querySelector('.hero-section')
  heroSection.addEventListener('mouseenter', stopAutoPlay)
  heroSection.addEventListener('mouseleave', startAutoPlay)

  // Start auto-play
  startAutoPlay()
}

// Animate current slide elements
function animateCurrentSlide() {
  const currentSlide = document.querySelector('.hero-slide.active')
  if (!currentSlide) return

  // Remove existing animation classes
  const animatedElements = currentSlide.querySelectorAll('.hero-animate-badge, .hero-animate-headline, .hero-animate-description, .hero-animate-actions, .hero-animate-image')
  animatedElements.forEach(el => {
    el.classList.remove('hero-animate-badge', 'hero-animate-headline', 'hero-animate-description', 'hero-animate-actions', 'hero-animate-image')
  })

  // Re-add animation classes with a small delay
  setTimeout(() => {
    const badge = currentSlide.querySelector('.badge')
    const headline = currentSlide.querySelector('.hero-headline')
    const description = currentSlide.querySelector('.hero-description')
    const actions = currentSlide.querySelector('.hero-actions')
    const imageContainer = currentSlide.querySelector('.hero-image-container, .video-placeholder')

    if (badge) badge.classList.add('hero-animate-badge')
    if (headline) headline.classList.add('hero-animate-headline')
    if (description) description.classList.add('hero-animate-description')
    if (actions) actions.classList.add('hero-animate-actions')
    if (imageContainer) imageContainer.classList.add('hero-animate-image')
  }, 50)
}
