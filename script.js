document.addEventListener("DOMContentLoaded", () => {
  const hamburgerMenu = document.querySelector(".hamburger-menu")
  const navLinks = document.querySelector(".nav-links")
  const header = document.querySelector(".header")

  // Hero Section Animation
  initHeroAnimations()

  // Create a mobile navigation overlay
  const navOverlay = document.createElement("div")
  navOverlay.classList.add("nav-overlay")
  navOverlay.innerHTML = `
        <a href="#features">Features</a>
        <a href="#testimonials">Testimonials</a>
        <a href="#pricing">Pricing</a>
        <a href="#contact">Contact</a>
        <button class="btn btn-ghost">Sign In</button>
        <button class="btn btn-primary">Start Free Trial</button>
    `
  document.body.appendChild(navOverlay)

  hamburgerMenu.addEventListener("click", () => {
    hamburgerMenu.classList.toggle("open")
    navOverlay.classList.toggle("open")
    document.body.classList.toggle("no-scroll") // Prevent scrolling when menu is open
  })

  // Close mobile menu when a link is clicked
  navOverlay.querySelectorAll("a").forEach((link) => {
    link.addEventListener("click", () => {
      hamburgerMenu.classList.remove("open")
      navOverlay.classList.remove("open")
      document.body.classList.remove("no-scroll")
    })
  })

  // Close mobile menu when "Sign In" or "Start Free Trial" buttons are clicked in the overlay
  navOverlay.querySelectorAll(".btn").forEach((button) => {
    button.addEventListener("click", () => {
      hamburgerMenu.classList.remove("open")
      navOverlay.classList.remove("open")
      document.body.classList.remove("no-scroll")
    })
  })

  // Add a class to body to prevent scrolling when mobile menu is open
  const style = document.createElement("style")
  style.innerHTML = `
        body.no-scroll {
            overflow: hidden;
        }
    `
  document.head.appendChild(style)
})

// Hero Section Animation Function
function initHeroAnimations() {
  // Get hero section elements
  const badge = document.querySelector(".hero-section .badge")
  const headline = document.querySelector(".hero-headline")
  const description = document.querySelector(".hero-description")
  const actions = document.querySelector(".hero-actions")
  const imageContainer = document.querySelector(".hero-image-container")

  // Add animation classes to trigger the animations
  if (badge) {
    badge.classList.add("hero-animate-badge")
  }

  if (headline) {
    headline.classList.add("hero-animate-headline")
  }

  if (description) {
    description.classList.add("hero-animate-description")
  }

  if (actions) {
    actions.classList.add("hero-animate-actions")
  }

  if (imageContainer) {
    imageContainer.classList.add("hero-animate-image")
  }
}
