/* Basic Reset & Global Styles */
:root {
  --primary-gradient-start: #2563eb; /* blue-600 */
  --primary-gradient-end: #8b5cf6; /* purple-600 */
  --primary-gradient-start-hover: #1d4ed8; /* blue-700 */
  --primary-gradient-end-hover: #7c3aed; /* purple-700 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-400: #9ca3af;
  --gray-600: #4b5563;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --blue-100: #dbeafe;
  --blue-600: #2563eb;
  --green-100: #dcfce7;
  --green-500: #22c55e;
  --green-600: #16a34a;
  --purple-100: #ede9fe;
  --purple-600: #8b5cf6;
  --orange-100: #ffedd5;
  --orange-600: #ea580c;
  --yellow-400: #facc15;
  --white: #ffffff;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans",
    "Helvetica Neue", sans-serif;
  line-height: 1.5;
  color: var(--gray-900);
  background-color: var(--white);
}

.container {
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.625rem 1.25rem; /* py-2.5 px-5 */
  border-radius: 0.5rem; /* rounded-lg */
  font-weight: 600; /* font-semibold */
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: 1px solid transparent;
  text-decoration: none; /* For anchor tags acting as buttons */
}

.btn-primary {
  background-image: linear-gradient(to right, var(--primary-gradient-start), var(--primary-gradient-end));
  color: var(--white);
}

.btn-primary:hover {
  background-image: linear-gradient(to right, var(--primary-gradient-start-hover), var(--primary-gradient-end-hover));
}

.btn-outline {
  background-color: transparent;
  border-color: var(--gray-200); /* Assuming a light gray border */
  color: var(--gray-900);
}

.btn-outline:hover {
  background-color: var(--gray-100);
  color: var(--gray-900);
}

.btn-ghost {
  background-color: transparent;
  border-color: transparent;
  color: var(--gray-900);
}

.btn-ghost:hover {
  background-color: var(--gray-100);
}

.btn-lg {
  padding: 0.75rem 1.5rem; /* py-3 px-6 */
  font-size: 1.125rem; /* text-lg */
}

.btn-full {
  width: 100%;
}

.btn-white {
  background-color: var(--white);
  color: var(--blue-600);
}

.btn-white:hover {
  background-color: var(--gray-100);
}

.btn-outline-white {
  background-color: transparent;
  border-color: var(--white);
  color: var(--white);
}

.btn-outline-white:hover {
  background-color: var(--white);
  color: var(--blue-600);
}

/* Badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem; /* px-3 py-1 */
  border-radius: 9999px; /* rounded-full */
  font-size: 0.875rem; /* text-sm */
  font-weight: 500; /* font-medium */
  background-color: var(--gray-100);
  color: var(--gray-800);
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: 4rem; /* mb-16 */
}

.section-title {
  font-size: 1.875rem; /* text-3xl */
  font-weight: 700; /* font-bold */
  color: var(--gray-900);
  margin-bottom: 1rem; /* mb-4 */
}

.section-description {
  font-size: 1.25rem; /* text-xl */
  color: var(--gray-600);
  max-width: 48rem; /* max-w-2xl */
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 640px) {
  .section-title {
    font-size: 2.25rem; /* sm:text-4xl */
  }
}

/* Header */
.header {
  border-bottom: 1px solid var(--gray-200);
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  position: sticky;
  top: 0;
  z-index: 50;
}

.header .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* space-x-2 */
}

.logo-icon {
  width: 2rem; /* w-8 */
  height: 2rem; /* h-8 */
  background-image: linear-gradient(to right, var(--blue-600), var(--purple-600));
  border-radius: 0.5rem; /* rounded-lg */
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-icon svg {
  color: var(--white);
}

.company-name {
  font-size: 1.25rem; /* text-xl */
  font-weight: 700; /* font-bold */
  color: var(--gray-900);
}

.nav-links {
  display: none; /* Hidden by default on mobile */
  align-items: center;
  gap: 2rem; /* space-x-8 */
}

.nav-links a {
  color: var(--gray-600);
  text-decoration: none;
  transition: color 0.2s ease-in-out;
}

.nav-links a:hover {
  color: var(--gray-900);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem; /* space-x-4 */
}

.header-actions .btn-ghost {
  display: none; /* Hidden by default on mobile */
}

.hamburger-menu {
  display: flex; /* Visible by default on mobile */
  flex-direction: column;
  justify-content: space-around;
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
  z-index: 60;
}

.hamburger-icon {
  display: block;
  width: 100%;
  height: 2px;
  background: var(--gray-900);
  border-radius: 1px;
  transition: all 0.3s linear;
  position: relative;
  transform-origin: 1px;
}

.hamburger-icon:nth-child(2) {
  opacity: 1;
}

.hamburger-icon:nth-child(1) {
  transform: translateY(-6px);
}

.hamburger-icon:nth-child(3) {
  transform: translateY(6px);
}

.hamburger-menu.open .hamburger-icon:nth-child(1) {
  transform: rotate(45deg);
}

.hamburger-menu.open .hamburger-icon:nth-child(2) {
  opacity: 0;
}

.hamburger-menu.open .hamburger-icon:nth-child(3) {
  transform: rotate(-45deg);
}

/* Mobile Nav Overlay */
.nav-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--white);
  z-index: 55;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transform: translateX(100%);
  transition: transform 0.3s ease-in-out;
}

.nav-overlay.open {
  transform: translateX(0);
}

.nav-overlay a {
  font-size: 1.5rem;
  color: var(--gray-900);
  text-decoration: none;
  margin-bottom: 1.5rem;
}

@media (min-width: 768px) {
  /* md breakpoint */
  .nav-links {
    display: flex;
  }
  .header-actions .btn-ghost {
    display: inline-flex;
  }
  .hamburger-menu {
    display: none;
  }
}

/* Hero Section */
.hero-section {
  padding-top: 5rem; /* pt-20 */
  padding-bottom: 4rem; /* pb-16 */
  text-align: center;
  overflow: hidden; /* Prevent horizontal scrollbar during animations */
}

/* Hero Animation Keyframes */
@keyframes slideInFromTop {
  0% {
    opacity: 0;
    transform: translateY(-50px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromBottom {
  0% {
    opacity: 0;
    transform: translateY(50px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Hero Animation Classes */
.hero-animate-badge {
  animation: slideInFromTop 0.8s ease-out forwards;
  animation-delay: 0.2s;
  opacity: 0;
}

.hero-animate-headline {
  animation: slideInFromLeft 0.8s ease-out forwards;
  animation-delay: 0.4s;
  opacity: 0;
}

.hero-animate-description {
  animation: slideInFromRight 0.8s ease-out forwards;
  animation-delay: 0.6s;
  opacity: 0;
}

.hero-animate-actions {
  animation: slideInFromBottom 0.8s ease-out forwards;
  animation-delay: 0.8s;
  opacity: 0;
}

.hero-animate-image {
  animation: fadeInScale 1s ease-out forwards;
  animation-delay: 1s;
  opacity: 0;
}

@media (min-width: 640px) {
  .hero-section {
    padding-top: 6rem; /* sm:pt-24 */
    padding-bottom: 5rem; /* sm:pb-20 */
  }
}

.hero-section .badge {
  margin-bottom: 1rem; /* mb-4 */
}

.hero-headline {
  font-size: 2.25rem; /* text-4xl */
  font-weight: 700; /* font-bold */
  color: var(--gray-900);
  margin-bottom: 1.5rem; /* mb-6 */
  max-width: 64rem; /* max-w-4xl */
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 640px) {
  .hero-headline {
    font-size: 3rem; /* sm:text-5xl */
  }
}

@media (min-width: 1024px) {
  .hero-headline {
    font-size: 3.75rem; /* lg:text-6xl */
  }
}

.gradient-text {
  background-image: linear-gradient(to right, var(--blue-600), var(--purple-600));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.hero-description {
  font-size: 1.25rem; /* text-xl */
  color: var(--gray-600);
  margin-bottom: 2rem; /* mb-8 */
  max-width: 48rem; /* max-w-2xl */
  margin-left: auto;
  margin-right: auto;
}

.hero-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem; /* gap-4 */
  justify-content: center;
}

@media (min-width: 640px) {
  .hero-actions {
    flex-direction: row;
  }
}

.hero-image-container {
  margin-top: 3rem; /* mt-12 */
}

.hero-image {
  border-radius: 0.5rem; /* rounded-lg */
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); /* shadow-2xl */
  max-width: 100%;
  height: auto;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

/* Features Section */
.features-section {
  padding-top: 5rem; /* py-20 */
  padding-bottom: 5rem; /* py-20 */
  background-color: var(--gray-50);
}

.features-grid {
  display: grid;
  gap: 2rem; /* gap-8 */
}

@media (min-width: 768px) {
  /* md breakpoint */
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  /* lg breakpoint */
  .features-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.feature-card {
  border: 1px solid var(--gray-200); /* border */
  border-radius: 0.5rem; /* rounded-lg */
  padding: 1.5rem; /* p-6 */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); /* shadow-lg */
  transition: box-shadow 0.2s ease-in-out;
  background-color: var(--white);
}

.feature-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* hover:shadow-xl */
}

.feature-icon {
  width: 3rem; /* w-12 */
  height: 3rem; /* h-12 */
  border-radius: 0.5rem; /* rounded-lg */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem; /* mb-4 */
}

.feature-icon svg {
  width: 1.5rem; /* w-6 */
  height: 1.5rem; /* h-6 */
}

.feature-icon.bg-blue-100 {
  background-color: var(--blue-100);
}
.feature-icon.bg-green-100 {
  background-color: var(--green-100);
}
.feature-icon.bg-purple-100 {
  background-color: var(--purple-100);
}
.feature-icon.bg-orange-100 {
  background-color: var(--orange-100);
}

.feature-icon svg.text-blue-600 {
  color: var(--blue-600);
}
.feature-icon svg.text-green-600 {
  color: var(--green-600);
}
.feature-icon svg.text-purple-600 {
  color: var(--purple-600);
}
.feature-icon svg.text-orange-600 {
  color: var(--orange-600);
}

.card-title {
  font-size: 1.125rem; /* text-lg */
  font-weight: 600; /* font-semibold */
  color: var(--gray-900);
  margin-bottom: 0.5rem; /* mb-2 */
}

.card-description {
  font-size: 0.875rem; /* text-sm */
  color: var(--gray-600);
}

/* Testimonials Section */
.testimonials-section {
  padding-top: 5rem; /* py-20 */
  padding-bottom: 5rem; /* py-20 */
}

.testimonials-grid {
  display: grid;
  gap: 2rem; /* gap-8 */
}

@media (min-width: 768px) {
  /* md breakpoint */
  .testimonials-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.testimonial-card {
  border: 1px solid var(--gray-200); /* border */
  border-radius: 0.5rem; /* rounded-lg */
  padding: 1.5rem; /* p-6 */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); /* shadow-lg */
  background-color: var(--white);
}

.testimonial-card .stars {
  display: flex;
  margin-bottom: 1rem; /* mb-4 */
}

.testimonial-card .stars svg {
  color: var(--yellow-400);
  fill: currentColor;
  width: 1.25rem; /* w-5 */
  height: 1.25rem; /* h-5 */
}

.testimonial-card .quote {
  color: var(--gray-600);
  margin-bottom: 1rem; /* mb-4 */
}

.testimonial-card .author-info {
  display: flex;
  align-items: center;
}

.testimonial-card .author-avatar {
  width: 2.5rem; /* w-10 */
  height: 2.5rem; /* h-10 */
  border-radius: 9999px; /* rounded-full */
  margin-right: 0.75rem; /* mr-3 */
}

.testimonial-card .author-name {
  font-weight: 600; /* font-semibold */
  color: var(--gray-900);
}

.testimonial-card .author-title {
  font-size: 0.875rem; /* text-sm */
  color: var(--gray-600);
}

/* Pricing Section */
.pricing-section {
  padding-top: 5rem; /* py-20 */
  padding-bottom: 5rem; /* py-20 */
  background-color: var(--gray-50);
}

.pricing-grid {
  display: grid;
  gap: 2rem; /* gap-8 */
  max-width: 64rem; /* max-w-5xl */
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 768px) {
  /* md breakpoint */
  .pricing-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.pricing-card {
  border: 1px solid var(--gray-200); /* border */
  border-radius: 0.5rem; /* rounded-lg */
  padding: 1.5rem; /* p-6 */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); /* shadow-lg */
  background-color: var(--white);
  display: flex;
  flex-direction: column;
  justify-content: space-between; /* Pushes button to bottom */
  position: relative;
}

.pricing-card.popular-card {
  border: 2px solid var(--blue-500);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-xl */
}

.popular-badge {
  position: absolute;
  top: -1rem; /* -top-4 */
  left: 50%;
  transform: translateX(-50%);
  background-image: linear-gradient(to right, var(--blue-600), var(--purple-600));
  color: var(--white);
  padding: 0.25rem 0.75rem; /* px-3 py-1 */
  border-radius: 9999px; /* rounded-full */
  font-size: 0.875rem; /* text-sm */
  font-weight: 500; /* font-medium */
  white-space: nowrap;
}

.pricing-card .card-title {
  font-size: 1.5rem; /* text-2xl */
  font-weight: 700; /* font-bold */
  margin-bottom: 1rem; /* mb-4 */
}

.pricing-card .price-display {
  margin-bottom: 1rem; /* mb-4 */
}

.pricing-card .price-amount {
  font-size: 2.25rem; /* text-4xl */
  font-weight: 700; /* font-bold */
  color: var(--gray-900);
}

.pricing-card .price-period {
  color: var(--gray-600);
}

.pricing-card .card-description {
  margin-bottom: 1.5rem; /* mb-6 */
}

.feature-list {
  list-style: none;
  padding: 0;
  margin-bottom: 1.5rem; /* mb-6 */
  display: flex;
  flex-direction: column;
  gap: 0.75rem; /* space-y-3 */
}

.feature-list li {
  display: flex;
  align-items: center;
  color: var(--gray-700); /* Assuming a slightly darker gray for list items */
}

.feature-list li svg {
  width: 1.25rem; /* w-5 */
  height: 1.25rem; /* h-5 */
  color: var(--green-500);
  margin-right: 0.5rem; /* mr-2 */
}

/* Final CTA Section */
.final-cta-section {
  padding-top: 5rem; /* py-20 */
  padding-bottom: 5rem; /* py-20 */
  background-image: linear-gradient(to right, var(--blue-600), var(--purple-600));
  text-align: center;
}

.final-cta-section .section-title {
  color: var(--white);
}

.final-cta-section .section-description {
  color: var(--blue-100);
}

.final-cta-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem; /* gap-4 */
  justify-content: center;
}

@media (min-width: 640px) {
  .final-cta-actions {
    flex-direction: row;
  }
}

/* Footer */
.footer {
  background-color: var(--gray-900);
  color: var(--white);
  padding-top: 3rem; /* py-12 */
  padding-bottom: 3rem; /* py-12 */
}

.footer-grid {
  display: grid;
  gap: 2rem; /* gap-8 */
}

@media (min-width: 768px) {
  /* md breakpoint */
  .footer-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.footer .logo-container .company-name {
  color: var(--white);
}

.footer-description {
  color: var(--gray-400);
  margin-bottom: 1rem; /* mb-4 */
}

.footer-heading {
  font-weight: 600; /* font-semibold */
  margin-bottom: 1rem; /* mb-4 */
}

.footer-links {
  list-style: none;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem; /* space-y-2 */
}

.footer-links a {
  color: var(--gray-400);
  text-decoration: none;
  transition: color 0.2s ease-in-out;
}

.footer-links a:hover {
  color: var(--white);
}

.footer-bottom {
  border-top: 1px solid var(--gray-800);
  margin-top: 2rem; /* mt-8 */
  padding-top: 2rem; /* pt-8 */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}

@media (min-width: 640px) {
  .footer-bottom {
    flex-direction: row;
  }
}

.copyright {
  color: var(--gray-400);
}

.social-icons {
  display: flex;
  gap: 1rem; /* space-x-4 */
  margin-top: 1rem; /* mt-4 */
}

@media (min-width: 640px) {
  .social-icons {
    margin-top: 0;
  }
}

.social-icon {
  width: 1.25rem; /* w-5 */
  height: 1.25rem; /* h-5 */
  color: var(--gray-400);
  transition: color 0.2s ease-in-out;
}

.social-icon:hover {
  color: var(--white);
}
